﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Newtonsoft.Json;
using CodeStage.AntiCheat.ObscuredTypes;
using System.Globalization;
using System.Threading.Tasks;
using UnityEngine.SceneManagement;
using Image = UnityEngine.UI.Image;

public enum Panels { MainMenu = 0, SelectVehicle = 1, SelectLevel = 2, Settings = 3 }

public class MainMenu : MonoBehaviour
{
    //private int gameScore { get; set; }

    public Animator FadeBackGround;

    public AudioSource menuMusic;
    public MenuPanels menuPanels;
    public MenuGUI menuGUI;
    public LevelSetting[] levelSetting;

    [System.Serializable]
    public class MenuGUI
    {
        public Slider sensitivity;

        public Toggle audio;
        public Toggle music;
        public Toggle vibrateToggle;
        public Toggle ButtonMode, AccelMode;

        public Image loadingBar;

        public GameObject loading;
    }

    [System.Serializable]
    public class MenuPanels
    {
        public GameObject MainMenu;
        public GameObject SelectVehicle;
        public GameObject SelectLevel;
        public GameObject EnoughMoney;
        public GameObject Settings;
    }

    // La clase VehicleSetting se ha trasladado a PurchaseCar.cs

    [System.Serializable]
    public class LevelSetting
    {
        public bool locked = false;
        public Button panel;

    }

    private Panels activePanel = Panels.MainMenu;
    private int currentLevelNumber = 2;
    private bool startingGame = false;

    private float menuLoadTime = 0.0f;
    private AsyncOperation sceneLoadingOperation = null;


    //ControlMode//////////////////////////////////////////////////////////////////////////////////////////////////////////////

    public void ControlModeButtons(Toggle value)
    {
        if (value.isOn)
            PlayerPrefs.SetString("ControlMode", "Buttons");
    }
    public void ControlModeAccel(Toggle value)
    {
        if (value.isOn)
            PlayerPrefs.SetString("ControlMode", "Accel");
    }


    public void DisableVibration(Toggle toggle)
    {
        if (toggle.isOn)
            PlayerPrefs.SetInt("VibrationActive", 0);
        else
            PlayerPrefs.SetInt("VibrationActive", 1);
    }

    // Los métodos de personalización de vehículos se han trasladado a PurchaseCar.cs


    //Share//////////////////////////////////////////////////////////////////////////////////////////////////////////////


    public void SettingActive(bool activePanel)
    {
        menuPanels.Settings.gameObject.SetActive(activePanel);
    }

    public void ClickExitButton()
    {
        Application.Quit();

    }

    //GamePanels//////////////////////////////////////////////////////////////////////////////////////////////////////////////

    public void CurrentPanel(int current)
    {
        activePanel = (Panels)current;

        switch (activePanel)
        {
            case Panels.MainMenu:
                menuPanels.MainMenu.SetActive(true);
                menuPanels.SelectVehicle.SetActive(false);
                menuPanels.SelectLevel.SetActive(false);
                break;
            case Panels.SelectVehicle:
                menuPanels.MainMenu.gameObject.SetActive(false);
                menuPanels.SelectVehicle.SetActive(true);
                menuPanels.SelectLevel.SetActive(false);
                break;
            case Panels.SelectLevel:
                menuPanels.MainMenu.SetActive(false);
                menuPanels.SelectVehicle.SetActive(false);
                menuPanels.SelectLevel.SetActive(true);
                break;
            case Panels.Settings:
                menuPanels.MainMenu.SetActive(false);
                menuPanels.SelectVehicle.SetActive(false);
                menuPanels.SelectLevel.SetActive(false);
                break;
        }
    }


    private void Start()
    {
        CultureInfo.DefaultThreadCurrentCulture = CultureInfo.InvariantCulture;
        GetSaeveitem();
    }



    // Método vacío para mantener compatibilidad con botones existentes
    public void checkerBuy()
    {
        // Este método está vacío porque la funcionalidad se ha trasladado a PurchaseCar.cs
        Debug.Log("checkerBuy method called but functionality has been moved to PurchaseCar.cs");

        // Mostrar un mensaje al usuario
        if (menuPanels != null && menuPanels.EnoughMoney != null)
        {
            menuPanels.EnoughMoney.SetActive(true);
        }
    }

    // Método vacío para mantener compatibilidad con botones existentes
    public void closeLoadingPay()
    {
        // LoadingPay has been removed
        Debug.Log("closeLoadingPay called but LoadingPay has been removed");
    }


    // Los métodos NextVehicle y PreviousVehicle se han trasladado a PurchaseCar.cs


    //GameSettings//////////////////////////////////////////////////////////////////////////////////////////////////////////////
    public void QualitySetting(int quality)
    {
        QualitySettings.SetQualityLevel(quality - 1, true);
        PlayerPrefs.SetInt("QualitySettings", quality);
    }

    public void EditSensitivity()
    {
        PlayerPrefs.SetFloat("Sensitivity", menuGUI.sensitivity.value);
    }

    public void DisableAudioButton(Toggle toggle)
    {
        if (toggle.isOn)
        {
            AudioListener.volume = 1;
            PlayerPrefs.SetInt("AudioActive", 0);
        }
        else
        {
            AudioListener.volume = 0;
            PlayerPrefs.SetInt("AudioActive", 1);
        }
    }


    public void DisableMusicButton(Toggle toggle)
    {
        if (toggle.isOn)
        {
            menuMusic.GetComponent<AudioSource>().mute = false;
            PlayerPrefs.SetInt("MusicActive", 0);
        }
        else
        {
            menuMusic.GetComponent<AudioSource>().mute = true;
            PlayerPrefs.SetInt("MusicActive", 1);
        }
    }


    public void EraseSave()
    {
        PlayerPrefs.DeleteAll();
        SceneManager.LoadScene(0);
    }

    public void Logout()
    {
        PlayerPrefs.DeleteKey("YourLogin");
        SceneManager.LoadScene(0);
    }

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    public void StartGame()
    {
        if (startingGame) return;
        FadeBackGround.SetBool("FadeOut", true);
        StartCoroutine(LoadLevelGame(1.5f));
        startingGame = true;
    }


    IEnumerator LoadLevelGame(float waitTime)
    {
        yield return new WaitForSeconds(waitTime);
        menuGUI.loading.SetActive(true);
        StartCoroutine(LoadLevelAsync());

    }

    IEnumerator LoadLevelAsync()
    {

        yield return new WaitForSeconds(0.4f);

        sceneLoadingOperation = SceneManager.LoadSceneAsync(currentLevelNumber + 1);
        sceneLoadingOperation.allowSceneActivation = false;

        while (!sceneLoadingOperation.isDone || sceneLoadingOperation.progress < 0.9f)
        {
            menuLoadTime += Time.deltaTime;

            yield return 0;
        }
    }


    public void currentLevel(int current)
    {
        currentLevelNumber = current;
        PlayerPrefs.SetInt("CurrentLevelNumber", currentLevelNumber);
    }




    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    void Awake()
    {
        //fricarr();
        // LoadingData has been removed

        AudioListener.pause = false;
        Time.timeScale = 1.0f;


        menuGUI.vibrateToggle.isOn = (PlayerPrefs.GetInt("VibrationActive") == 0) ? true : false;


        //gameScore = 999999;
        CurrentPanel(0);

        if (PlayerPrefs.GetInt("QualitySettings") == 0)
        {
            PlayerPrefs.SetInt("QualitySettings", 4);
            QualitySettings.SetQualityLevel(3, true);
        }
        else
        {
            QualitySettings.SetQualityLevel(PlayerPrefs.GetInt("QualitySettings") - 1, true);
        }

        if (PlayerPrefs.GetFloat("Sensitivity") == 0.0f)
        {
            menuGUI.sensitivity.value = 1.0f;
            PlayerPrefs.SetFloat("Sensitivity", 1.0f);
        }
        else
        {
            menuGUI.sensitivity.value = PlayerPrefs.GetFloat("Sensitivity");
        }


        switch (PlayerPrefs.GetString("ControlMode"))
        {
            case "":
                menuGUI.ButtonMode.isOn = true;
                break;
            case "Buttons":
                menuGUI.ButtonMode.isOn = true;
                break;
            case "Accel":
                menuGUI.AccelMode.isOn = true;
                break;
        }


        currentLevelNumber = PlayerPrefs.GetInt("CurrentLevelNumber");

        for (int lvls = 0; lvls < levelSetting.Length; lvls++)
        {
            if (lvls <= PlayerPrefs.GetInt("CurrentLevelUnlocked"))
                levelSetting[lvls].locked = false;

        }

        currentLevel(currentLevelNumber);


        switch (PlayerPrefs.GetString("ControlMode"))
        {
            case "":
                PlayerPrefs.SetString("ControlMode", "Buttons");
                menuGUI.ButtonMode.isOn = true;
                break;
            case "Buttons":
                menuGUI.ButtonMode.isOn = true;
                break;
            case "Accel":
                menuGUI.AccelMode.isOn = true;
                break;
        }

        //audio and music Toggle
        menuGUI.audio.isOn = (PlayerPrefs.GetInt("AudioActive") == 0) ? true : false;
        AudioListener.volume = (PlayerPrefs.GetInt("AudioActive") == 0) ? 1.0f : 0.0f;

        menuGUI.music.isOn = (PlayerPrefs.GetInt("MusicActive") == 0) ? true : false;
        menuMusic.mute = (PlayerPrefs.GetInt("MusicActive") == 0) ? false : true;
    }

    public void GetSaeveitem()
    {
        // Este método se ha simplificado ya que la funcionalidad de vehículos se ha trasladado a PurchaseCar.cs
        // LoadingData has been removed
        Debug.Log("GetSaeveitem called but LoadingData has been removed");
    }

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    void Update()
    {
        if (sceneLoadingOperation != null)
        {
            menuGUI.loadingBar.fillAmount = Mathf.MoveTowards(menuGUI.loadingBar.fillAmount, sceneLoadingOperation.progress + 0.2f, Time.deltaTime * 0.5f);

            if (menuGUI.loadingBar.fillAmount > sceneLoadingOperation.progress)
                sceneLoadingOperation.allowSceneActivation = true;
        }

        // La funcionalidad de actualización de vehículos y rotación de cámara se ha trasladado a PurchaseCar.cs
    }



}
