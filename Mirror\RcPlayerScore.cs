using UnityEngine;
using Mirror;

namespace Mirror.Examples.MultipleAdditiveScenes
{
    /// <summary>
    /// Custom PlayerScore class with setter methods to avoid cross-assembly SyncVar modification errors
    /// This is a copy of the original PlayerScore with added setter methods
    /// </summary>
    public class RcPlayerScore : NetworkBehaviour
    {
        [SyncVar]
        public int playerNumber = 0;

        [SyncVar]
        public int scoreIndex = 0;

        [SyncVar]
        public int matchIndex = 0;

        [SyncVar]
        public uint score = 0;

        /// <summary>
        /// Setter method for playerNumber to avoid cross-assembly SyncVar modification
        /// </summary>
        /// <param name="value">The new player number value</param>
        public void SetplayerNumber(int value)
        {
            if (isServer)
            {
                playerNumber = value;
                Debug.Log($"[RcPlayerScore] Set playerNumber to {value}");
            }
            else
            {
                Debug.LogWarning("[RcPlayerScore] SetplayerNumber called on client - only server can modify SyncVars");
            }
        }

        /// <summary>
        /// Setter method for scoreIndex to avoid cross-assembly SyncVar modification
        /// </summary>
        /// <param name="value">The new score index value</param>
        public void SetscoreIndex(int value)
        {
            if (isServer)
            {
                scoreIndex = value;
                Debug.Log($"[RcPlayerScore] Set scoreIndex to {value}");
            }
            else
            {
                Debug.LogWarning("[RcPlayerScore] SetscoreIndex called on client - only server can modify SyncVars");
            }
        }

        /// <summary>
        /// Setter method for matchIndex to avoid cross-assembly SyncVar modification
        /// </summary>
        /// <param name="value">The new match index value</param>
        public void SetmatchIndex(int value)
        {
            if (isServer)
            {
                matchIndex = value;
                Debug.Log($"[RcPlayerScore] Set matchIndex to {value}");
            }
            else
            {
                Debug.LogWarning("[RcPlayerScore] SetmatchIndex called on client - only server can modify SyncVars");
            }
        }

        /// <summary>
        /// Setter method for score to avoid cross-assembly SyncVar modification
        /// </summary>
        /// <param name="value">The new score value</param>
        public void SetScore(uint value)
        {
            if (isServer)
            {
                score = value;
                Debug.Log($"[RcPlayerScore] Set score to {value}");
            }
            else
            {
                Debug.LogWarning("[RcPlayerScore] SetScore called on client - only server can modify SyncVars");
            }
        }

        /// <summary>
        /// Add to the current score
        /// </summary>
        /// <param name="points">Points to add to the score</param>
        public void AddScore(uint points)
        {
            if (isServer)
            {
                score += points;
                Debug.Log($"[RcPlayerScore] Added {points} points, new score: {score}");
            }
            else
            {
                Debug.LogWarning("[RcPlayerScore] AddScore called on client - only server can modify SyncVars");
            }
        }

        /// <summary>
        /// Reset the score to zero
        /// </summary>
        public void ResetScore()
        {
            if (isServer)
            {
                score = 0;
                Debug.Log("[RcPlayerScore] Score reset to 0");
            }
            else
            {
                Debug.LogWarning("[RcPlayerScore] ResetScore called on client - only server can modify SyncVars");
            }
        }

        /// <summary>
        /// Get player info as formatted string
        /// </summary>
        /// <returns>Formatted player information</returns>
        public string GetPlayerInfo()
        {
            return $"Player {playerNumber} (Score: {score}, Match: {matchIndex}, Index: {scoreIndex})";
        }

        // Unity lifecycle methods
        void Start()
        {
            Debug.Log($"[RcPlayerScore] Started for {gameObject.name} - {GetPlayerInfo()}");
        }

        // Network lifecycle methods
        public override void OnStartServer()
        {
            Debug.Log($"[RcPlayerScore] Server started for {gameObject.name}");
        }

        public override void OnStartClient()
        {
            Debug.Log($"[RcPlayerScore] Client started for {gameObject.name} - {GetPlayerInfo()}");
        }

        public override void OnStopServer()
        {
            Debug.Log($"[RcPlayerScore] Server stopped for {gameObject.name}");
        }

        public override void OnStopClient()
        {
            Debug.Log($"[RcPlayerScore] Client stopped for {gameObject.name}");
        }
    }
}
