# Secure Stake Amount Implementation

## Overview
This document describes the secure implementation for retrieving stake amounts from RaceManager's verified stake data in MultiSceneNetManager with security measures to prevent user manipulation. The system no longer requires any UI display fields as stake amounts are retrieved programmatically.

## Key Changes Made

### 1. RaceManager.cs Security Enhancements

#### Singleton Pattern
- Added `public static RaceManager Instance { get; private set; }`
- Implemented proper singleton initialization in `Awake()`
- Added cleanup in `OnDestroy()`

#### Security Properties
- `IsStakeVerified`: Returns true only if stake has been server-verified
- `VerifiedStakeAmount`: Returns stake amount only if verified, otherwise 0
- `StakeAmountDisplayText`: Provides access to the display text from stakeAmountText

### 2. MultiSceneNetManager.cs Security Implementation

#### Field Changes
- Removed `stakInputField` completely (no longer needed)
- Stake amounts are now retrieved programmatically without UI dependencies

#### Secure Stake Retrieval System
Implemented `GetSecureStakeAmount()` with 3 security layers:

1. **Layer 1 (Most Secure)**: RaceManager verified stake
   - Only returns stake if server-verified
   - Direct access to RaceManager.Instance

2. **Layer 2 (Secure)**: Encrypted PlayerPrefs
   - Decrypts stake from encrypted storage
   - Uses SessionManager encryption key

3. **Layer 3 (Fallback)**: Regular PlayerPrefs
   - Backward compatibility only
   - Logs warning about security level

#### Security Methods Added
- `GetVerifiedStakeFromRaceManager()`: Reflection-based singleton access (cross-namespace)
- `GetDecryptedStakeFromPlayerPrefs()`: Encrypted fallback
- `GetEncryptionKey()`: Retrieves key from SessionManager
- `DecryptStakeAmount()`: XOR decryption implementation

#### Network Security
- Updated `SendStakToServer()` to use secure stake retrieval
- No UI dependencies - purely programmatic approach
- Eliminates any possibility of user manipulation through UI

## Security Features

### Prevention of User Manipulation
1. **No UI Dependencies**: Completely eliminates user interface manipulation
2. **Server Verification**: Only verified stakes are used
3. **Encrypted Storage**: Sensitive data encrypted when stored
4. **Audit Trail**: Comprehensive logging of security layer usage

### Multi-Layer Security
1. **Primary**: Server-verified stake from RaceManager
2. **Secondary**: Encrypted PlayerPrefs with SessionManager key
3. **Tertiary**: Regular PlayerPrefs (backward compatibility)

## Usage Instructions

### For Developers
1. **No UI Setup Required**: System works purely programmatically
2. **Scene Setup**: Ensure RaceManager is present in race scenes
3. **Integration**: No additional setup needed - system works automatically

### For Integration
- System works automatically once RaceManager is in scene
- Monitor logs to verify security layer usage
- Ensure RaceManager singleton is properly initialized

## Security Benefits

1. **Eliminates UI Manipulation**: No user interface dependencies whatsoever
2. **Server Verification**: Only server-verified stakes used for multiplayer
3. **Encrypted Storage**: Local data protected with encryption
4. **Audit Logging**: Clear visibility into which security layer is used
5. **Backward Compatibility**: Graceful fallback for existing data
6. **Simplified Architecture**: Cleaner code without UI management overhead

## Implementation Notes

- RaceManager must be present in race scenes for optimal security
- No UI setup required - purely programmatic approach
- Comprehensive error handling prevents crashes
- Detailed logging aids in debugging and security auditing
- Uses reflection for cross-namespace access (RaceManager in global namespace, MultiSceneNetManager in Mirror namespace)
- Eliminates all potential UI-based attack vectors

## Security Considerations

1. **Primary Security**: Always rely on RaceManager.Instance.VerifiedStakeAmount
2. **Fallback Usage**: Encrypted PlayerPrefs only when RaceManager unavailable
3. **Legacy Support**: Regular PlayerPrefs for backward compatibility only
4. **Server Verification**: Critical for preventing fraud in multiplayer games

This implementation ensures that stake amounts cannot be manipulated by users while maintaining system reliability and backward compatibility.
